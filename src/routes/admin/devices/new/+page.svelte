<script lang="ts">
  import { enhance } from "$app/forms";
  import { goto } from "$app/navigation";
  import type { PageData } from "./$types";

  export let data: PageData;
  
  let errorMessage: string | null = null;
  let loading = false;

  // Form fields
  let device = {
    team_id: data.teams?.[0]?.id || '',
    ip: '',
    nickname: '',
    role: 'Single',
    vpn_conf: '',
    msg_conf: '',
    phone_conf: ''
  };

  // Helper function to handle form submission start
  function handleSubmit() {
    loading = true;
    errorMessage = null;
  }

  // Helper function to handle form result
  function handleResult(result: any) {
    loading = false;

    if (result.type === "failure") {
      // Access the error message from result.data
      errorMessage = result.data?.error || "An unknown error occurred during submission.";
    } else if (result.type === "success") {
      errorMessage = ""; // Clear error on success
      // Navigate to the customers list page after successful creation
      goto('/admin/devices');
    } else if (result.type === "error") {
      // Handle other potential error types if needed
      errorMessage = `Server error: ${result.status}`; // Example of handling a generic error type
    } else if (result.type === "redirect") { // Explicitly handle redirect type
      errorMessage = ""; // Clear error message on redirect
      goto(result.location, { invalidateAll: true });
    }
  }


  const handleEnhance = () => {
    handleSubmit();
    return async ({ result }: { result: any }) => {
      handleResult(result);
    };
  };
</script>

<svelte:head>
  <title>Phantom - Admin Panel | Add Device</title>
</svelte:head>

<div class="admin-container admin-card">
  <div class="admin-title">Add Device</div>
  <hr style="margin-top: 1rem; margin-bottom: 1rem; border-color: #333;" />

  <form method="POST" use:enhance={handleEnhance}>
    <div class="form-group">
      <label for="team_id" class="form-label">Team</label>
      <select
        id="team_id"
        name="team_id"
        bind:value={device.team_id}
        class="form-input"
        required
      >
        {#if data.teams?.length > 0}
          {#each data.teams as team}
            <option value={team.id}>
              {team.id}
            </option>
          {/each}
        {:else}
          <option value="" disabled>No teams available</option>
        {/if}
      </select>
    </div>

    <div class="form-group">
      <label for="ip" class="form-label">IP Address:</label>
      <input 
        id="ip"
        class="form-input" 
        bind:value={device.ip} 
        name="ip"
      />
    </div>

    <div class="form-group">
      <label for="nickname" class="form-label">Nickname (Optional):</label>
      <input 
        id="nickname"
        class="form-input" 
        bind:value={device.nickname} 
        name="nickname"
        placeholder="e.g., Valeron Solevoy's phone"
      />
    </div>

    <div class="form-group">
      <label for="role" class="form-label">Role:</label>
      <select
        id="role"
        name="role"
        bind:value={device.role}
        class="form-input"
        required
      >
        <option value="" disabled>Select Role</option>
        <option value="Single">Single</option>
        <option value="TeamLead">Team Lead</option>
        <option value="TeamMember">Team Member</option>
      </select>
    </div>

    <div class="form-group">
      <label for="vpn_conf" class="form-label">VPN Configuration:</label>
      <textarea 
        id="vpn_conf"
        class="form-input" 
        bind:value={device.vpn_conf} 
        rows="4"
        name="vpn_conf"
        style="font-family: monospace;"
        placeholder="Paste VPN configuration here..."
      ></textarea>
    </div>

    <div class="form-group">
      <label for="msg_conf" class="form-label">Message Configuration:</label>
      <textarea 
        id="msg_conf"
        class="form-input" 
        bind:value={device.msg_conf} 
        rows="4"
        name="msg_conf"
        style="font-family: monospace;"
        placeholder="Paste message configuration here..."
      ></textarea>
    </div>

    <div class="form-group">
      <label for="phone_conf" class="form-label">Phone Configuration:</label>
      <textarea 
        id="phone_conf"
        class="form-input" 
        bind:value={device.phone_conf} 
        rows="4"
        name="phone_conf"
        style="font-family: monospace;"
        placeholder="Paste phone configuration here..."
      ></textarea>
    </div>

    <div class="form-actions">
      <button
        class="button button-secondary"
        type="button"
        on:click={() => goto("/admin/devices")}
      >
        Cancel
      </button>
      <button class="button" type="submit" disabled={loading}>
        {loading ? 'Adding...' : 'Add Device'}
      </button>
    </div>
  </form>

  {#if errorMessage}
    <div class="message-container">
      <div class="message error">
        {errorMessage}
      </div>
    </div>
  {/if}
</div>
