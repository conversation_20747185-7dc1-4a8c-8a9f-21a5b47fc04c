/**
 * Wallet entity model
 * Represents a cryptocurrency wallet with team relationships
 */
export class Wallet {
  /**
   * @param {Object} data - Wallet data
   * @param {number} data.internal_id - Internal ID
   * @param {Date} data.created_at - Creation timestamp
   * @param {string} data.team_internal_id - Team's internal ID
   * @param {string} data.team_id - Team's public ID
   * @param {string} data.currency - Cryptocurrency currency code
   * @param {string} data.address - Wallet address
   */
  constructor(data = {}) {
    this.internal_id = data.internal_id;
    this.created_at = data.created_at ? new Date(data.created_at) : null;
    this.team_internal_id = data.team_internal_id;
    this.team_id = data.team_id;
    this.currency = data.currency;
    this.address = data.address;
    
    // Related data (populated by joins)
    this.team = data.team || null;
  }

  /**
   * Supported cryptocurrency currencies
   */
  static SUPPORTED_CURRENCIES = [
    'USDTTRC',
    'TRX',
    'TON',
    'SOL',
    'NOT',
    'XMR',
    'XRP',
    'DOGE',
  ];

  /**
   * Validate wallet data
   * @param {Partial<Wallet>} data - Wallet data to validate
   * @param {string} operation - Operation type (create, update)
   * @returns {{valid: boolean, errors: string[]}}
   */
  static validate(data, operation = 'create') {
    const errors = [];

    if (operation === 'create') {
      if (!data.team_internal_id || typeof data.team_internal_id !== 'string') {
        errors.push('Team internal ID is required and must be a string');
      }
      
      if (!data.team_id || typeof data.team_id !== 'string') {
        errors.push('Team ID is required and must be a string');
      }

      if (!data.currency || typeof data.currency !== 'string') {
        errors.push('Currency is required and must be a string');
      }

      if (!data.address || typeof data.address !== 'string') {
        errors.push('Wallet address is required and must be a string');
      }
    }

    if (data.currency && !Wallet.SUPPORTED_CURRENCIES.includes(data.currency)) {
      errors.push(`Currency must be one of: ${Wallet.SUPPORTED_CURRENCIES.join(', ')}`);
    }

    if (data.address && (typeof data.address !== 'string' || data.address.trim().length === 0)) {
      errors.push('Wallet address must be a non-empty string');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate wallet address format for specific currency
   * @param {string} address - Wallet address
   * @param {string} currency - Currency code
   * @returns {boolean}
   */
  static isValidAddress(address, currency) {
    if (!address || typeof address !== 'string') return false;

    // Basic validation - can be enhanced with currency-specific rules
    switch (currency) {
      case 'USDTTRC':
      case 'TRX':
        // TRON addresses start with 'T' and are 34 characters
        return /^T[A-Za-z0-9]{33}$/.test(address);
      
      case 'SOL':
        // Solana addresses are base58 encoded, typically 32-44 characters
        return /^[1-9A-HJ-NP-Za-km-z]{32,44}$/.test(address);
      
      case 'XRP':
        // Ripple addresses start with 'r' and are typically 25-34 characters
        return /^r[1-9A-HJ-NP-Za-km-z]{24,33}$/.test(address);
      
      case 'DOGE':
        // Dogecoin addresses start with 'D' or 'A' and are 34 characters
        return /^[DA][1-9A-HJ-NP-Za-km-z]{33}$/.test(address);
      
      default:
        // Generic validation for other currencies
        return address.length >= 20 && address.length <= 100;
    }
  }

  /**
   * Convert to database format
   * @returns {Object}
   */
  toDb() {
    return {
      internal_id: this.internal_id,
      created_at: this.created_at?.toISOString(),
      team_internal_id: this.team_internal_id,
      team_id: this.team_id,
      currency: this.currency,
      address: this.address
    };
  }

  /**
   * Create from database data
   * @param {Object} dbData - Raw database data
   * @returns {Wallet}
   */
  static fromDb(dbData) {
    return new Wallet(dbData);
  }

  /**
   * Get wallet summary for API responses
   * @returns {Object}
   */
  toSummary() {
    return {
      internal_id: this.internal_id,
      currency: this.currency,
      address: this.address,
      team_id: this.team_id,
      created_at: this.created_at?.toISOString()
    };
  }

  /**
   * Get display name for currency
   * @returns {string}
   */
  getCurrencyDisplayName() {
    const displayNames = {
      'USDTTRC': 'USDT (TRC-20)',
      'TRX': 'TRON',
      'TON': 'Toncoin',
      'SOL': 'Solana',
      'NOT': 'Notcoin',
      'XMR': 'Monero',
      'XRP': 'Ripple',
      'DOGE': 'Dogecoin'
    };
    return displayNames[this.currency] || this.currency;
  }

  /**
   * Check if wallet is for a stablecoin
   * @returns {boolean}
   */
  isStablecoin() {
    const stablecoins = ['USDTTRC'];
    return stablecoins.includes(this.currency);
  }
}

/**
 * Wallet creation data interface
 */
export class CreateWalletData {
  /**
   * @param {Object} data
   * @param {string} data.team_internal_id - Team's internal ID
   * @param {string} data.team_id - Team's public ID
   * @param {string} data.currency - Cryptocurrency currency code
   * @param {string} data.address - Wallet address
   */
  constructor(data) {
    this.team_internal_id = data.team_internal_id;
    this.team_id = data.team_id;
    this.currency = data.currency;
    this.address = data.address;
    this.created_at = new Date();
  }
}

/**
 * Wallet update data interface
 */
export class UpdateWalletData {
  /**
   * @param {Object} data
   * @param {string} data.address - Wallet address
   */
  constructor(data) {
    if (data.address !== undefined) this.address = data.address;
  }
}

/**
 * Wallet statistics interface
 */
export class WalletStatistics {
  /**
   * @param {Object} data
   * @param {number} data.totalWallets - Total number of wallets
   * @param {Object} data.walletsByCurrency - Wallets grouped by currency
   * @param {number} data.teamsWithWallets - Teams that have wallets
   */
  constructor(data) {
    this.totalWallets = data.totalWallets || 0;
    this.walletsByCurrency = data.walletsByCurrency || {};
    this.teamsWithWallets = data.teamsWithWallets || 0;
  }
}
