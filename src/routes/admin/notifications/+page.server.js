import { fail, redirect } from '@sveltejs/kit';
import { getDeviceRepository } from '$lib/server/database/DatabaseFactory.js';
import { QueryOptions } from '$lib/server/database/interfaces/IDatabase.js';

/** @type {import('./$types').PageServerLoad} */
export async function load() {
  try {
    const deviceRepository = getDeviceRepository();

    // Load all devices with team information for the dropdown
    const devicesResult = await deviceRepository.findWithTeamInfo(new QueryOptions({
      orderBy: { team_id: 'asc' }
    }));

    if (!devicesResult.success) {
      console.error('Error loading devices:', devicesResult.error);
      return {
        devices: [],
        error: 'Failed to load devices',
      };
    }

    const devices = devicesResult.data || [];

    // Format devices for dropdown with IP first
    const formattedDevices = devices.map((device) => ({
      internal_id: device.internal_id,
      ip: device.ip,
      nickname: device.nickname,
      team_id: device.team_id,
      has_fcm_token: !!device.fcm_token,
      display_name: `${device.ip || 'Unknown IP'}${device.nickname ? ` (${device.nickname})` : ''} - ${device.team_id}`,
      team_info: device.team,
    }));

    return {
      devices: formattedDevices,
    };
  } catch (err) {
    console.error('Error in load function:', err);
    return {
      devices: [],
      error: 'Failed to load page data',
    };
  }
}

/** @type {import('./$types').Actions} */
export const actions = {
  sendNotification: async ({ request, locals, fetch, cookies }) => {
    const form = await request.formData();
    const title = form.get('title');
    const message = form.get('message');
    const selectedDevice = form.get('selectedDevice'); // This will be device internal_id

    console.log('[sendNotification] Form data:', {
      title,
      message,
      selectedDevice,
    });

    const authToken = process.env.WEBVIEW_AUTH_TOKEN;
    if (!authToken && process.env.NODE_ENV !== 'development') {
      console.log('No auth token found');
      return fail(401, { error: 'Unauthorized: No auth token found' });
    }

    try {
      if (!selectedDevice) {
        return fail(400, { error: 'Please select a device' });
      }

      // Get device info to determine team_id automatically and send to specific device
      const deviceRepository = getDeviceRepository();
      const deviceResult = await deviceRepository.findById(selectedDevice);

      if (!deviceResult.success || !deviceResult.data) {
        return fail(400, { error: 'Selected device not found' });
      }

      const device = deviceResult.data;

      if (!device.fcm_token) {
        return fail(400, {
          error: `Device ${device.nickname || device.ip} does not have an FCM token`,
        });
      }

      const teamId = device.team_id;
      const deviceId = device.ip; // Use IP as device identifier for notifications

      console.log(
        '[sendNotification] Sending to specific device:',
        deviceId,
        'in team:',
        teamId
      );

      const notificationOptions = {
        title: String(title),
        message: String(message),
        teamId,
        deviceId,
      };

      const result = await import('$lib/server/notifications').then((mod) =>
        mod.sendNotification(notificationOptions)
      );

      console.log('[sendNotification] Success:', result);
      return {
        success: true,
        response: result?.summary || result?.response || 'Notification sent',
        details: result,
        sentTo: `Device ${deviceId} in team ${teamId}`,
      };
    } catch (err) {
      console.error('[sendNotification] Error:', err);
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to send notification';
      return fail(500, { error: errorMessage });
    }
  },
};
