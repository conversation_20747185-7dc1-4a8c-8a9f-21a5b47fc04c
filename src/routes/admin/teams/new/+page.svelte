<script>
  import { goto } from "$app/navigation";
  import { enhance } from "$app/forms";

  let team = {
    id: "",
    balance: 0,
  };

  let loading = false; // Added loading state
  let errorMessage = "";

  // Helper function to handle form submission start
  function handleSubmit() {
    loading = true;
    errorMessage = ""; // Clear previous error message (changed from null)
  }

  // Helper function to handle form result
  /**
   * @param {import('@sveltejs/kit').ActionResult} result
   */
  function handleResult(result) {
    loading = false; // Turn off loading

    if (result.type === "failure") {
      // Access the error message from result.data
      errorMessage = result.data?.error || "An unknown error occurred during submission.";
    } else if (result.type === "success") {
      errorMessage = ""; // Clear error on success
      // Navigate to the teams list page after successful creation
      goto('/admin/teams');
    } else if (result.type === "error") {
      // Handle other potential error types if needed
      errorMessage = `Server error: ${result.status}`; // Example of handling a generic error type
    } else if (result.type === "redirect") { // Explicitly handle redirect type
      errorMessage = ""; // Clear error message on redirect
      goto(result.location, { invalidateAll: true });
    }
  }

  const handleEnhance = (/* form element, not strictly needed here */) => {
    handleSubmit(); // Call handleSubmit before the actual submission

    return async ({ result } /*: { result: import('@sveltejs/kit').ActionResult } */) => {
      // This async function runs after the server responds
      handleResult(result);
    };
  };
</script>

<svelte:head>
  <title>Phantom - Admin Panel | Add Team</title>
</svelte:head>

<div class="admin-container admin-card">
  <div class="admin-title">Add team</div>
  <hr style="margin-top: 1rem; margin-bottom: 1rem; border-color: #333;" />

  <form
    method="POST"
    use:enhance={handleEnhance}
  >
    <div class="form-group">
      <label class="form-label">ID:</label>
      <input class="form-input" bind:value={team.id} required name="id" />
    </div>
    <div class="form-group">
      <label class="form-label">Balance:</label>
      <input
        class="form-input"
        type="number"
        bind:value={team.balance}
        required
        name="balance"
      />
    </div>

    <div class="form-actions">
      <button
        class="button button-secondary"
        type="button"
        on:click={() => goto("/admin/teams/" + (team.id || ""))
        }>Cancel</button
      >
      <button class="button" type="submit" disabled={loading}>
        <!-- Disable button while loading -->
        {loading ? "Adding..." : "Add"}
      </button>
    </div>
  </form>

  {#if errorMessage}
    <div class="message-container">
      <div class="message error">
        {errorMessage}
      </div>
    </div>
  {/if}
</div>
