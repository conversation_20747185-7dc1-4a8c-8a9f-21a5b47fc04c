import { getDeviceRepository } from '$lib/server/database/DatabaseFactory.js';
import { QueryOptions } from '$lib/server/database/interfaces/IDatabase.js';

export async function load() {
  try {
    const deviceRepository = getDeviceRepository();

    // Get device statistics and all devices with team info
    const [
      deviceStatsResult,
      devicesWithTeamResult
    ] = await Promise.all([
      deviceRepository.getStatistics(),
      deviceRepository.findWithTeamInfo(new QueryOptions({
        orderBy: { created_at: 'desc' }
      }))
    ]);

    if (!deviceStatsResult.success) {
      console.error('Error getting device statistics:', deviceStatsResult.error);
      throw new Error('Failed to load device statistics');
    }

    if (!devicesWithTeamResult.success) {
      console.error('Error fetching devices:', devicesWithTeamResult.error);
      throw new Error('Failed to load device data');
    }

    const deviceStats = deviceStatsResult.data;
    const devices = devicesWithTeamResult.data || [];

    // Get the most recent device
    const recentDevice = devices.length > 0 ? devices[0] : null;

    return {
      devices: devices.map(device => device.toSummary ? device.toSummary() : device),
      totalDevices: deviceStats.totalDevices,
      recentDevice: recentDevice ? (recentDevice.toSummary ? recentDevice.toSummary() : recentDevice) : null,
      error: null,
    };
  } catch (error) {
    console.error('Error in devices page load:', error);
    return {
      devices: [],
      totalDevices: 0,
      recentDevice: null,
      error:
        error instanceof Error ? error.message : 'An unknown error occurred',
    };
  }
}

export const actions = {
  delete: async ({ request }) => {
    const formData = await request.formData();
    const id = formData.get('id');

    if (!id || typeof id !== 'string') {
      return { success: false, error: 'Missing or invalid device ID' };
    }

    try {
      const deviceRepository = getDeviceRepository();
      const deleteResult = await deviceRepository.deleteById(id);

      if (!deleteResult.success) {
        console.error('Error deleting device:', deleteResult.error);
        return { success: false, error: deleteResult.error?.message || 'Failed to delete device' };
      }

      return { success: true };
    } catch (error) {
      console.error('Error deleting device:', error);
      return { success: false, error: error.message };
    }
  },
};
