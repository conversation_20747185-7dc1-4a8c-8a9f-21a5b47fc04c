import { minify } from 'html-minifier';
import { initializeDatabaseFactory } from '$lib/server/database/DatabaseFactory.js';

const minifyOpts = {
  collapseBooleanAttributes: true,
  collapseWhitespace: true,
  conservativeCollapse: true,
  decodeEntities: true,
  html5: true,
  ignoreCustomComments: [/^#/],
  minifyCSS: true,
  minifyJS: false,
  removeAttributeQuotes: true,
  removeComments: false,
  removeOptionalTags: true,
  removeRedundantAttributes: true,
  removeScriptTypeAttributes: true,
  removeStyleLinkTypeAttributes: true,
  sortAttributes: true,
  sortClassName: true,
};

const NOT_FOUND_RESPONSE = new Response('', { status: 404 });

let databaseInitialized = false;
async function ensureDatabaseInitialized() {
  if (databaseInitialized) {
    return;
  }

  try {
    await initializeDatabaseFactory();
    databaseInitialized = true;
  } catch (error) {
    console.error('Failed to initialize database factory:', error);
    // Don't throw here, let individual requests handle the error
  }
}

export const handleError = () => {
  return NOT_FOUND_RESPONSE;
};

export const handle = async ({ event, resolve }) => {
  try {
    if (!databaseInitialized) {
      await ensureDatabaseInitialized();
    }

    return await resolve(event, {
      transformPageChunk: ({ html, done }) => {
        return minify(html, minifyOpts);
      },
    });
  } catch (e) {
    console.error('Error in handle:', e);
    return NOT_FOUND_RESPONSE;
  }
};
