# Phantom Web Admin

A comprehensive admin dashboard and API system for managing VPN teams, devices, wallets, and transactions with enterprise-grade analytics and database abstraction.

## Project Components

* **Admin Dashboard** - Enterprise-grade statistics dashboard with comprehensive analytics
* **Database Abstraction Layer** - Repository pattern enabling database provider switching
* **API Endpoints** - RESTful APIs for mobile app and VPN node integration
* **Nginx Configuration** - Production-ready web server setup
* **Installation Scripts** - Automated deployment tools

### Supported Integrations

* **[Phantom Android App](https://github.com/3geeze/phantom-android-app)** - Mobile billing application
  * FCM token registration
  * WestWallet IPN processing
* **[Phantom VPN Node](https://github.com/3geeze/phantom-vpn-node)** - VPN server management

## Script Installations

### Install on VPN node running WireGuard

```shell
curl -sL https://3geese.net/install/phantom-vpn-node.sh | bash
```

### Install APK repacking dependencies

```shell
curl -sL https://3geese.net/install/install-repack-apk-deps.sh | bash
```

### Repack an APK example

```shell
./repack_apk.sh vpn.apk -f vpn.conf -n test.apk -k key.keystore -s signer.jar -a key0 -p 123321
```

## Tech Stack

*   **Framework:** [SvelteKit](https://kit.svelte.dev/)
*   **Bundler:** [Vite](https://vitejs.dev/)
*   **Package Manager:** [Bun](https://bun.sh/)
*   **Database Abstraction:** Repository Pattern with Provider Switching
*   **Current Provider:** [Supabase](https://supabase.io/)
*   **Future Providers:** PostgreSQL, MySQL, SQLite (via abstraction layer)

## Architecture Overview

### Database Abstraction Layer

The application implements a comprehensive repository pattern that provides:

- **Database Independence** - Switch between providers without code changes
- **Type Safety** - Entity models with business logic validation
- **Comprehensive Analytics** - Advanced statistics and reporting capabilities
- **Error Handling** - Standardized error objects and validation

### Repository Pattern Structure

```
src/lib/server/database/
├── interfaces/          # Repository and database interfaces
├── entities/           # Entity models with validation
├── repositories/       # Provider-specific implementations
└── DatabaseFactory.js # Dependency injection container
```

### Supported Entities

- **Teams** - VPN team management with balance tracking
- **Devices** - Device registration and activity monitoring
- **Wallets** - Multi-currency wallet management
- **Transactions** - Payment processing and analytics

## Getting Started

### Prerequisites

*   [Node.js](https://nodejs.org/) (v18 or higher)
*   [Bun](https://bun.sh/)

### Installation

1.  Clone the repository:
    ```bash
    git clone <repository-url>
    ```
2.  Navigate to the project directory:
    ```bash
    cd phantom-web-admin
    ```
3.  Install the dependencies:
    ```bash
    bun install
    ```
4.  Create a `.env` file by copying the example:
    ```bash
    cp .env.example .env
    ```
5.  Update the `.env` file with your credentials and database provider.

### Database Configuration

The application supports multiple database providers through environment configuration:

```bash
# Database provider selection
DATABASE_PROVIDER=supabase  # Options: supabase, postgresql, mysql, sqlite

# Supabase configuration (when using Supabase provider)
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_KEY=your_supabase_service_key
```

### Running in Development Mode

To start the development server, run:

```bash
bun run dev
```

The application will be available at `http://localhost:5173`.

## Usage Examples

### Repository Pattern Usage

```javascript
import { getTeamRepository, getDatabaseFactory } from '$lib/server/database/DatabaseFactory.js';

// Initialize database factory (do this once at app startup)
await getDatabaseFactory().initialize();

// Get repository and perform operations
const teamRepository = getTeamRepository();

// Find team by ID
const team = await teamRepository.findByTeamId('team-123');

// Get team statistics
const stats = await teamRepository.getStatistics();

// Create new team
const newTeam = await teamRepository.create({
  id: 'new-team',
  balance: 100,
  owner_id: 'owner-123'
});
```

### Service Layer Usage

```javascript
import { createTeam, deleteTeamWithAssociatedData } from '$lib/server/teamService.js';

// Create team with validation
const result = await createTeam({
  id: 'team-456',
  balance: 50,
  owner_id: 'owner-456'
});

// Delete team and all associated data
const deleteResult = await deleteTeamWithAssociatedData('team-456');
```

## Available Scripts

*   `bun run dev`: Starts the development server.
*   `bun run build`: Creates a production-ready build of the application.
*   `bun run preview`: Starts a local server to preview the production build.

## Building for Production

To build the application for production, run:

```bash
bun run build
```

The build artifacts will be stored in the `build/` directory.

## Database Migration

### Switching Database Providers

The repository pattern enables seamless switching between database providers:

1. **Update Environment Variable:**
   ```bash
   DATABASE_PROVIDER=postgresql  # Switch from supabase to postgresql
   ```

2. **Add Provider Configuration:**
   ```bash
   # PostgreSQL configuration
   DATABASE_URL=postgresql://user:password@localhost:5432/phantom
   ```

3. **Restart Application** - No code changes required!

### Supported Providers

- ✅ **Supabase** - Fully implemented and production-ready
- ⏳ **PostgreSQL** - Architecture ready, implementation pending
- ⏳ **MySQL** - Architecture ready, implementation pending
- ⏳ **SQLite** - Architecture ready, implementation pending

## Features

### Admin Dashboard

- **Team Management** - CRUD operations with balance tracking
- **Device Analytics** - Registration monitoring and activity tracking
- **Wallet Operations** - Multi-currency support and transaction history
- **Enterprise Statistics** - Comprehensive analytics and reporting
- **Real-time Monitoring** - Live updates and health checks

### API Endpoints

- **Team API** - Team creation, updates, and statistics
- **Notification API** - FCM token management and push notifications
- **Transaction API** - Payment processing and IPN handling

### Security Features

- **CSRF Protection** - Cross-site request forgery prevention
- **Session Management** - Secure session handling
- **Admin Authentication** - Password-protected admin access
- **API Key Authentication** - Secure API endpoint access

## Development Status

### ✅ Completed (Production Ready)
- Database abstraction layer (100%)
- Entity models and validation (100%)
- Repository interfaces (100%)
- Supabase implementation (100%)
- Service layer refactoring (100%)
- Core admin pages (66%)

### 🔄 In Progress
- Admin dashboard completion (33%)

### ⏳ Pending
- API endpoint migration (0%)
- Alternative database providers (0%)
- Comprehensive testing (0%)

## Contributing

The repository pattern implementation provides a solid foundation for:
- Adding new database providers
- Extending entity models
- Implementing additional analytics
- Adding comprehensive testing

See `IMPLEMENTATION_ALIGNMENT.md` and `REPOSITORY_PATTERN_PROGRESS.md` for detailed progress tracking.