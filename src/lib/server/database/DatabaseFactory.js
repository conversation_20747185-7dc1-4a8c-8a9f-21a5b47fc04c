import { env } from '$env/dynamic/private';
import { createClient } from '@supabase/supabase-js';
import { SupabaseTeamRepository } from './repositories/supabase/SupabaseTeamRepository.js';
import { SupabaseDeviceRepository } from './repositories/supabase/SupabaseDeviceRepository.js';
import { SupabaseWalletRepository } from './repositories/supabase/SupabaseWalletRepository.js';
import { SupabaseTransactionRepository } from './repositories/supabase/SupabaseTransactionRepository.js';
import { SupabaseDatabase } from './repositories/supabase/SupabaseDatabase.js';

/**
 * Database provider types
 */
export const DATABASE_PROVIDERS = {
  SUPABASE: 'supabase',
  POSTGRESQL: 'postgresql',
  MYSQL: 'mysql',
  SQLITE: 'sqlite'
};

/**
 * Database factory for creating repository instances
 * Supports multiple database providers and handles dependency injection
 */
export class DatabaseFactory {
  constructor() {
    this.provider = env.DATABASE_PROVIDER || DATABASE_PROVIDERS.SUPABASE;
    this.repositories = new Map();
    this.databaseClient = null;
    this.initialized = false;
  }

  /**
   * Initialize the database connection and factory
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.initialized) {
      return;
    }

    try {
      switch (this.provider) {
        case DATABASE_PROVIDERS.SUPABASE:
          await this.initializeSupabase();
          break;
        case DATABASE_PROVIDERS.POSTGRESQL:
          await this.initializePostgreSQL();
          break;
        default:
          throw new Error(`Unsupported database provider: ${this.provider}`);
      }

      this.initialized = true;
      console.log(`Database factory initialized with provider: ${this.provider}`);
    } catch (error) {
      console.error('Failed to initialize database factory:', error);
      throw error;
    }
  }

  /**
   * Initialize Supabase client and repositories
   * @private
   */
  async initializeSupabase() {
    if (!env.SUPABASE_URL || !env.SUPABASE_SERVICE_KEY) {
      throw new Error('Missing SUPABASE_URL or SUPABASE_SERVICE_KEY environment variables');
    }

    const supabaseClient = createClient(env.SUPABASE_URL, env.SUPABASE_SERVICE_KEY);

    // Create database wrapper
    this.databaseClient = new SupabaseDatabase(supabaseClient);
    await this.databaseClient.connect();

    // Initialize repositories
    this.repositories.set('team', new SupabaseTeamRepository(supabaseClient));
    this.repositories.set('device', new SupabaseDeviceRepository(supabaseClient));
    this.repositories.set('wallet', new SupabaseWalletRepository(supabaseClient));
    this.repositories.set('transaction', new SupabaseTransactionRepository(supabaseClient));
  }

  /**
   * Initialize PostgreSQL client and repositories
   * @private
   */
  async initializePostgreSQL() {
    // TODO: Implement PostgreSQL initialization
    throw new Error('PostgreSQL provider not yet implemented');
  }

  /**
   * Get repository instance by name
   * @param {string} repositoryName - Repository name ('team', 'device', 'wallet', 'transaction')
   * @returns {import('./interfaces/IRepository.js').IRepository}
   */
  getRepository(repositoryName) {
    if (!this.initialized) {
      throw new Error('Database factory not initialized. Call initialize() first.');
    }

    const repository = this.repositories.get(repositoryName);
    if (!repository) {
      throw new Error(`Repository '${repositoryName}' not found for provider '${this.provider}'`);
    }

    return repository;
  }

  /**
   * Get team repository
   * @returns {import('./interfaces/ITeamRepository.js').ITeamRepository}
   */
  getTeamRepository() {
    return this.getRepository('team');
  }

  /**
   * Get device repository
   * @returns {import('./interfaces/IDeviceRepository.js').IDeviceRepository}
   */
  getDeviceRepository() {
    return this.getRepository('device');
  }

  /**
   * Get wallet repository
   * @returns {import('./interfaces/IWalletRepository.js').IWalletRepository}
   */
  getWalletRepository() {
    return this.getRepository('wallet');
  }

  /**
   * Get transaction repository
   * @returns {import('./interfaces/ITransactionRepository.js').ITransactionRepository}
   */
  getTransactionRepository() {
    return this.getRepository('transaction');
  }

  /**
   * Get the current database provider
   * @returns {string}
   */
  getProvider() {
    return this.provider;
  }

  /**
   * Get the raw database client
   * @returns {any}
   */
  getDatabaseClient() {
    if (!this.initialized) {
      throw new Error('Database factory not initialized. Call initialize() first.');
    }
    return this.databaseClient;
  }

  /**
   * Check if the database connection is healthy
   * @returns {Promise<boolean>}
   */
  async isHealthy() {
    if (!this.initialized) {
      return false;
    }

    try {
      return await this.databaseClient.isHealthy();
    } catch (error) {
      console.error('Database health check failed:', error);
      return false;
    }
  }

  /**
   * Close database connections
   * @returns {Promise<void>}
   */
  async close() {
    if (!this.initialized) {
      return;
    }

    try {
      // Supabase client doesn't need explicit closing
      // Other providers might need cleanup here
      
      this.repositories.clear();
      this.databaseClient = null;
      this.initialized = false;
      
      console.log('Database factory closed');
    } catch (error) {
      console.error('Error closing database factory:', error);
      throw error;
    }
  }

  /**
   * Execute a transaction across multiple repositories
   * @param {Function} transactionFn - Function that receives repository instances
   * @returns {Promise<any>}
   */
  async executeTransaction(transactionFn) {
    if (!this.initialized) {
      throw new Error('Database factory not initialized. Call initialize() first.');
    }

    // TODO: Implement proper transaction support
    // For now, just execute the function with current repositories
    try {
      return await transactionFn({
        teamRepository: this.getTeamRepository(),
        deviceRepository: this.getDeviceRepository(),
        walletRepository: this.getWalletRepository(),
        transactionRepository: this.getTransactionRepository()
      });
    } catch (error) {
      console.error('Transaction failed:', error);
      throw error;
    }
  }
}

/**
 * Global database factory instance
 */
let globalDatabaseFactory = null;

/**
 * Get the global database factory instance
 * @returns {DatabaseFactory}
 */
export function getDatabaseFactory() {
  if (!globalDatabaseFactory) {
    globalDatabaseFactory = new DatabaseFactory();
  }
  return globalDatabaseFactory;
}

/**
 * Initialize the global database factory
 * @returns {Promise<DatabaseFactory>}
 */
export async function initializeDatabaseFactory() {
  const factory = getDatabaseFactory();
  await factory.initialize();
  return factory;
}

/**
 * Convenience function to get a repository
 * @param {string} repositoryName - Repository name
 * @returns {import('./interfaces/IRepository.js').IRepository}
 */
export function getRepository(repositoryName) {
  return getDatabaseFactory().getRepository(repositoryName);
}

/**
 * Convenience functions for specific repositories
 */
export function getTeamRepository() {
  return getDatabaseFactory().getTeamRepository();
}

export function getDeviceRepository() {
  return getDatabaseFactory().getDeviceRepository();
}

export function getWalletRepository() {
  return getDatabaseFactory().getWalletRepository();
}

export function getTransactionRepository() {
  return getDatabaseFactory().getTransactionRepository();
}
