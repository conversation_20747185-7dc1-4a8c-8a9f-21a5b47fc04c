<script lang="ts">
  import { enhance } from '$app/forms';
  import { goto } from '$app/navigation';
  import { invalidateAll } from '$app/navigation';
  import type { PageData } from './$types';
  
  // Define types
  type Device = {
    internal_id: string;
    team_id: string;
    ip?: string | null;
    nickname?: string | null;
    vpn_conf?: string | null;
    msg_conf?: string | null;
    phone_conf?: string | null;
    created_at: string;
    last_auth_at?: string | null;
    role?: string | null;
    teams?: {
      id: string;
      balance: number;
    };
  };
  
  // Role badge styling
  const getRoleBadgeClass = (role: string | null | undefined) => {
    if (!role) return 'role-badge';
    switch(role.toLowerCase()) {
      case 'admin':
        return 'role-badge role-admin';
      case 'user':
        return 'role-badge role-user';
      default:
        return 'role-badge';
    }
  };
  
  // Export page data with proper typing
  export let data: PageData;
  
  // Initialize with default values to prevent undefined errors
  let devices: Device[] = [];
  let error: string | null = null;
  let totalDevices = 0;
  let recentDevice: Device | null = null;
  
  function timeAgo(dateString: string | undefined) {
    if (!dateString) return '-';
    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffMs = now.getTime() - date.getTime();
      const seconds = Math.floor(diffMs / 1000);
      let interval = Math.floor(seconds / 31536000);
      if (interval > 1) return interval + ' years ago';
      interval = Math.floor(seconds / 2592000);
      if (interval > 1) return interval + ' months ago';
      interval = Math.floor(seconds / 86400);
      if (interval > 1) return interval + ' days ago';
      interval = Math.floor(seconds / 3600);
      if (interval > 1) return interval + ' hours ago';
      interval = Math.floor(seconds / 60);
      if (interval > 1) return interval + ' minutes ago';
      return Math.floor(seconds) + ' seconds ago';
    } catch {
      return 'Unknown time';
    }
  }
  
  // Update reactive variables when data changes
  $: if (data) {
    devices = data.devices || [];
    error = data.error || null;
    totalDevices = data.totalDevices || 0;
    recentDevice = data.recentDevice as Device | null;
  }
  
  function handleEdit(device: Device) {
    goto(`/admin/devices/${device.internal_id}`);
  }
  

  
  // Delete handler is now handled by the form action with enhance
  
  function handleAddDevice() {
    goto('/admin/devices/new');
  }
</script>

<svelte:head>
  <title>Phantom - Admin Panel | Devices</title>
</svelte:head>

<div class="admin-card">
  <div class="admin-card-header">
    <div class="admin-card-title">Devices</div>
    <div class="batch-actions">
      <button class="button add-team-button" on:click={handleAddDevice}>Add Device</button>
    </div>
  </div>
  <div class="admin-stats-compact">
    <div class="stats-summary">
      <div class="stat-item">
        <span class="stat-label">Total Devices:</span>
        <span class="stat-value-compact" style="color: #5b6eff;">{totalDevices}</span>
      </div>
    </div>
  </div>

  {#if error}
    <div class="alert alert-error">
      <div class="alert-content">
        <div class="alert-title">Error</div>
        <div class="alert-message">{error}</div>
      </div>
    </div>
  {/if}

  

  {#if error}
    <div class="error">{error}</div>
  {:else if devices.length === 0}
    <div class="empty-state">
      <p>No devices found. Add a device to get started.</p>
    </div>
  {:else}
  <table class="client-table">
    <style>
  .role-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: 600;
    text-transform: capitalize;
    background-color: #e2e8f0;
    color: #4a5568;
  }
  
  .role-admin {
    background-color: #fef2f2;
    color: #dc2626;
  }
  
  .role-user {
    background-color: #eff6ff;
    color: #2563eb;
  }
  
  .text-muted {
    color: #a0aec0;
  }
</style>
    <thead>
      <tr>
        <th style="padding: 4px 4px;">Actions</th>
        <th style="padding: 4px 4px;">IP</th>
        <th style="padding: 4px 4px;">Nickname</th>
        <th style="padding: 4px 4px;">Role</th>
        <th style="padding: 4px 4px;">Team ID</th>
        <th style="padding: 4px 4px;">Last Active</th>
        <th style="padding: 4px 4px;">Created</th>
      </tr>
    </thead>
    <tbody>
      {#each devices as device}
        <tr>
          <td style="padding: 4px 4px;">
            <div style="display: flex; gap: 4px;">
              <form method="POST" action="?/delete" use:enhance={({ cancel }) => {
                if (!confirm('Are you sure you want to delete this device?')) {
                  cancel();
                  return;
                }

                return async ({ result, update }) => {
                  if (result.type === 'success') {
                    invalidateAll();
                  } else if (result.type === 'error') {
                    console.error('Delete failed:', result.error);
                    error = result.error?.message || 'Failed to delete device';
                  }
                };
              }}>
                <input type="hidden" name="id" value="{device.internal_id}" />
                <button class="button button-small button-danger" type="submit" style="padding: 4px 8px; font-size: 1em;">
                  Delete
                </button>
              </form>
              <button class="button button-small" on:click={() => handleEdit(device)} style="padding: 4px 8px; font-size: 1em;">
                Edit
              </button>
            </div>
          </td>
          <td style="padding: 4px 4px; font-weight: bold; color: #ffd700;">{device.ip || '-'}</td>
          <td style="padding: 4px 4px;">{device.nickname || '-'}</td>
          <td style="padding: 4px 4px;">
            {#if device.role}
              <span class={getRoleBadgeClass(device.role)}>{device.role}</span>
            {:else}
              <span class="text-muted">-</span>
            {/if}
          </td>
          <td style="padding: 4px 4px;">
            {#if device.team_id}
              <a href="/admin/teams/{device.team_id}" class="text-link" target="_blank" rel="noopener noreferrer">{device.team_id}</a>
            {:else}
              -
            {/if}
          </td>
          <td style="padding: 4px 4px;">{device.last_auth_at ? timeAgo(device.last_auth_at) : 'Never'}</td>
          <td style="padding: 4px 4px;">{timeAgo(device.created_at)}</td>
        </tr>
      {/each}
    </tbody>
  </table>
  {/if}
</div>
