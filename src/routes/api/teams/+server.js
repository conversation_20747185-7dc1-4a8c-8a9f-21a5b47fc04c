import { json } from '@sveltejs/kit';
import { getTeamRepository } from '$lib/server/database/DatabaseFactory.js';
import { QueryOptions } from '$lib/server/database/interfaces/IDatabase.js';

/** @type {import('./$types').RequestHandler} */
export async function GET() {
  try {
    const teamRepository = getTeamRepository();

    const teamsResult = await teamRepository.findMany(new QueryOptions({
      select: ['id', 'balance', 'created_at'],
      orderBy: { created_at: 'desc' }
    }));

    if (!teamsResult.success) {
      console.error('Error fetching teams:', teamsResult.error);
      return json({ error: 'Failed to fetch teams' }, { status: 500 });
    }

    const teams = teamsResult.data?.map(team => team.toSummary ? team.toSummary() : team) || [];

    return json({
      success: true,
      teams: teams,
    });
  } catch (error) {
    console.error('Error in teams API:', error);
    return json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
