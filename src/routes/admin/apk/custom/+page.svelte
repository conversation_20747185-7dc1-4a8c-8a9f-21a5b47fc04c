<script>
  import { enhance } from "$app/forms";
  import { onMount } from "svelte";

  // Get the data from the server load function using Svelte 5 $props
  let { data, form } = $props();

  // Default configuration template
  const defaultConfig = `[Interface]
PrivateKey =
Address =
DNS = *******,*******

[Peer]
PublicKey =
PresharedKey =
Endpoint =
AllowedIPs = 0.0.0.0/0,::/0
`;

  // Local state using Svelte 5 $state
  let dragActive = $state(false);
  /** @type {File | null} */
  let selectedFile = $state(null);
  let configText = $state(defaultConfig); // Initialize with default config immediately
  let assetFilename = $state("vpn.conf");
  let processing = $state(false);
  /** @type {ReturnType<typeof setTimeout> | null} */
  let fileSelectDebounce = null; // Not reactive state, just a timeout ID
  let downloadUrl = $state("");
  let downloadFilename = $state("");
  let fileInputLocked = $state(false);
  let clientStartTime = 0; // Track when the client started the repacking process, likely not reactive UI state

  // Generate a unique component ID
  const componentId = "custom-apk-repacker-" + Math.random().toString(36).substring(2, 9);

  // Cleanup is now handled automatically by the APK Repacker Service

  // Component cleanup on mount
  onMount(() => {
    // Clean up any timeouts on component destroy
    return () => {
      if (fileSelectDebounce) {
        clearTimeout(fileSelectDebounce);
      }

      // Reset locks
      fileInputLocked = false;
    };
  });

  // Handle file selection
  /**
   * @param {Event} event
   */
  function handleFileSelect(event) {
    // Prevent multiple rapid file selections
    if (fileInputLocked) {
      console.log("File input locked, ignoring event");
      return;
    }

    // Lock the file input to prevent multiple selections
    fileInputLocked = true;

    // Clear any existing debounce
    if (fileSelectDebounce) {
      clearTimeout(fileSelectDebounce);
    }

    // Use debounce to prevent multiple rapid file selections
    fileSelectDebounce = setTimeout(() => {
      // @ts-ignore
      const files = event.target.files;
      if (files && files.length > 0) {
        const file = files[0];

        // Validate the file
        if (validateApkFile(file)) {
          selectedFile = file;

          // Log successful file selection
          console.log("File selected successfully:", {
            name: file.name,
            size: file.size,
            type: file.type,
          });
        } else {
          // Clear the file input if validation fails
          const fileInput = document.getElementById("apkFileInput");
          if (fileInput && fileInput instanceof HTMLInputElement) {
            fileInput.value = "";
          }
        }
      }

      // Unlock the file input after processing
      fileInputLocked = false;
      fileSelectDebounce = null;
    }, 100); // 100ms debounce
  }

  /**
   * Validate the APK file
   * @param {File} file - The file to validate
   * @returns {boolean} - Whether the file is valid
   */
  function validateApkFile(file) {
    // Check if the file has a valid APK extension
    const fileName = file.name.toLowerCase();
    if (!fileName.endsWith(".apk")) {
      alert("Please select a valid APK file (.apk extension)");
      return false;
    }

    // Check if the file has content
    if (file.size === 0) {
      alert("The selected APK file is empty");
      return false;
    }

    // Check if the file size is reasonable (at least 1MB for a valid APK)
    if (file.size < 1024 * 1024) {
      alert(
        `The selected APK file is too small (${(file.size / 1024).toFixed(2)} KB). A valid APK file should be at least 1MB.`
      );
      return false;
    }

    // Check if the file size is too large (over 200MB)
    const maxSize = 200 * 1024 * 1024; // 200MB
    if (file.size > maxSize) {
      alert(
        `The selected APK file is too large (${(file.size / (1024 * 1024)).toFixed(2)} MB). Maximum file size is 200MB.`
      );
      return false;
    }

    return true;
  }

  // Handle drag events
  /**
   * @param {DragEvent} event
   */
  function handleDragEnter(event) {
    event.preventDefault();
    event.stopPropagation();
    dragActive = true;
  }

  /**
   * @param {DragEvent} event
   */
  function handleDragOver(event) {
    event.preventDefault();
    event.stopPropagation();
    dragActive = true;
  }

  /**
   * @param {DragEvent} event
   */
  function handleDragLeave(event) {
    event.preventDefault();
    event.stopPropagation();
    dragActive = false;
  }

  /**
   * @param {DragEvent} event
   */
  function handleDrop(event) {
    event.preventDefault();
    event.stopPropagation();
    dragActive = false;

    try {
      if (event.dataTransfer) {
        const files = event.dataTransfer.files;
        console.log("Files dropped:", files.length);

        if (files && files.length > 0) {
          const file = files[0];
          console.log("Dropped file:", {
            name: file.name,
            size: file.size,
            type: file.type,
          });

          // Validate the file
          if (validateApkFile(file)) {
            // Update the file input to match the dropped file
            const fileInput = document.getElementById("apkFileInput");
            if (fileInput && fileInput instanceof HTMLInputElement) {
              // Create a DataTransfer object to set the files property
              const dataTransfer = new DataTransfer();
              dataTransfer.items.add(file);
              fileInput.files = dataTransfer.files;
            }

            // Set the selected file
            selectedFile = file;
            console.log("File selected via drag and drop");
          }
        }
      }
    } catch (error) {
      console.error("Error handling dropped file:", error);
    }
  }

  // Function removed - direct click handlers are used instead

  // Clear selected file
  function clearSelectedFile() {
    selectedFile = null;
    // Also clear the file input
    const fileInput = document.getElementById("apkFileInput");
    if (fileInput && fileInput instanceof HTMLInputElement) {
      fileInput.value = "";
    }
  }

  // Direct form submission as a fallback
  async function submitForm() {
    if (!selectedFile) {
      alert("Please select an APK file");
      return;
    }

    if (!configText) {
      alert("Please enter configuration");
      return;
    }

    processing = true;
    // Start client-side timer
    clientStartTime = Date.now();

    try {
      // Create a FormData object
      const formData = new FormData();
      formData.append("csrfToken", data.csrfToken);

      // Add the file to the form data
      try {
        formData.append("apkFile", selectedFile, selectedFile.name);
        console.log("Direct submission - file added to form data:", {
          name: selectedFile.name,
          size: selectedFile.size,
          type: selectedFile.type,
        });
      } catch (fileError) {
        console.error("Error adding file to form data:", fileError);
        alert(
          "Error adding file to form data. Please try again with a different file."
        );
        processing = false;
        return;
      }

      // Add the configuration to the form data
      formData.append("config", configText);

      // Add the asset filename to the form data
      formData.append("assetFilename", assetFilename);

      // Add the component ID to create a unique job ID
      formData.append("componentId", componentId);

      // Log form data entries to verify
      console.log(`[${componentId}] Direct submission - form data entries:`);
      for (const [key, value] of formData.entries()) {
        console.log(
          `${key}: ${value instanceof File ? `File: ${value.name} (${value.size} bytes)` : value}`
        );
      }

      // Submit the form data with timeout and error handling
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5 * 60 * 1000); // 5 minute timeout

      try {
        const response = await fetch("?/repackApk", {
          method: "POST",
          body: formData,
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        // Check for 413 Entity Too Large error
        if (response.status === 413) {
          throw new Error("File too large: The server rejected the upload because the file is too large (maximum 200MB).");
        }

        // Check for other HTTP errors
        if (!response.ok) {
          throw new Error(`Server error: ${response.status} ${response.statusText}`);
        }

        // Parse the response
        const result = await response.json();

        // Update form state
        form = result;

        // Handle success
        if (result.success) {
          if (result.jobId && result.apkName) {
            downloadUrl = `/admin/apk/download/${result.jobId}/${result.apkName}`;
            downloadFilename = result.apkName;

            // Calculate total client-side time (includes network transfer)
            const clientTotalTime = ((Date.now() - clientStartTime) / 1000).toFixed(2);

            // Use client-side timing for the user-facing message
            result.message = `APK repacked successfully in ${clientTotalTime} seconds`;

            // Log both client and server timing for diagnostic purposes
            if (result.timeInSeconds !== undefined) {
              console.log(`APK repacking times - Server: ${result.timeInSeconds}s, Total: ${clientTotalTime}s`);
            }
            // The layout will handle showing the toast based on the form data
          }
        } else {
          // Clear download link if there was an error
          downloadUrl = "";
          downloadFilename = "";
        }
      } catch (innerErr) {
        // Rethrow the error to be caught by the outer catch block
        throw innerErr;
      }
    } catch (err) {
      console.error("Error submitting form:", err);

      // Handle specific error types
      let errorMessage = "An unknown error occurred";

      if (err instanceof Error) {
        errorMessage = err.message || "Error submitting form";

        if (errorMessage.includes("File too large")) {
          form = {
            success: false,
            error: "File too large",
            details: "The server rejected the upload because the file is too large (maximum 200MB)."
          };
        } else if (err.name === "AbortError") {
          form = {
            success: false,
            error: "Upload timeout",
            details: "The upload took too long and was aborted. Try with a smaller file or check your connection."
          };
        } else {
          form = {
            success: false,
            error: "Error submitting form",
            details: errorMessage
          };
        }
      } else {
        form = {
          success: false,
          error: "Error submitting form",
          details: "An unknown error occurred"
        };
      }
    } finally {
      processing = false;
    }
  }

  // Handle form submission with enhance
  function handleSubmit() {
    processing = true;
    // Start client-side timer
    clientStartTime = Date.now();

    /**
     * @param {{ result: { type: string }, update: Function, formData: FormData }} param0
     */
    return async ({ result, update, formData }) => {
      try {
        // Make sure the file is included in the form data
        if (selectedFile) {
          console.log("Adding file to form data:", {
            name: selectedFile.name,
            size: selectedFile.size,
            type: selectedFile.type,
          });

          // Remove any existing file entry and add the selected file
          formData.delete("apkFile");

          // Add the component ID to create a unique job ID
          formData.set("componentId", componentId);

          // Use a different approach to add the file to the form data
          try {
            // First approach - standard
            formData.append("apkFile", selectedFile, selectedFile.name);

            // Log form data entries to verify
            console.log(`[${componentId}] Form data entries after append:`);
            for (const [key, value] of formData.entries()) {
              console.log(
                `${key}: ${value instanceof File ? `File: ${value.name} (${value.size} bytes)` : value}`
              );
            }
          } catch (appendError) {
            console.error("Error appending file to form data:", appendError);
            // Use direct submission as fallback
            submitForm();
            return;
          }
        } else {
          console.log("No file selected");
          // Use direct submission as fallback
          submitForm();
          return;
        }

        // Update the form result
        await update();
      } catch (error) {
        console.error("Error in form submission:", error);
        // Use direct submission as fallback
        submitForm();
        return;
      } finally {
        processing = false;
      }

      // Reset form state for subsequent submissions
      if (result.type === "success" && form?.success) {
        // Create download link for the repacked APK
        if (form.jobId && form.apkName) {
          downloadUrl = `/admin/apk/download/${form.jobId}/${form.apkName}`;
          downloadFilename = form.apkName;

          // Calculate total client-side time (includes network transfer)
          const clientTotalTime = ((Date.now() - clientStartTime) / 1000).toFixed(2);

          // Use client-side timing for the user-facing message
          form.message = `APK repacked successfully in ${clientTotalTime} seconds`;

          // Log both client and server timing for diagnostic purposes
          if (form.timeInSeconds !== undefined) {
            console.log(`APK repacking times - Server: ${form.timeInSeconds}s, Total: ${clientTotalTime}s`);
          }
          // The layout will handle showing the toast based on the form data
        }
      } else {
        // Clear download link if there was an error
        downloadUrl = "";
        downloadFilename = "";
        // The layout will handle showing the toast based on the form data
      }
    };
  }
</script>

<svelte:head>
  <title>Phantom - Admin Panel | Custom APK Repacker</title>
</svelte:head>

<!-- APK Repacker Card -->
<div class="admin-card">
  <div class="admin-card-header">
    <div class="admin-card-title">Custom APK Repacker</div>
  </div>
  <div class="admin-card-description">
    Upload an APK file, set the configuration, and click "Repack APK" to create
    a customized APK.
  </div>

  <div class="apk-repacker-container">
    <form
      method="POST"
      action="?/repackApk"
      enctype="multipart/form-data"
      use:enhance={handleSubmit}
      class="horizontal-layout"
    >
      <!-- CSRF Token -->
      {#if data.csrfToken}
        <input type="hidden" name="csrfToken" value={data.csrfToken} />
      {/if}

      <!-- File Upload Area -->
      <div class="form-group file-upload-group">
        <label for="apkFileInput" class="form-label">APK File</label>
        <div
          class="file-drop-area {dragActive ? 'active' : ''} {selectedFile
            ? 'has-file'
            : ''}"
          on:dragenter={handleDragEnter}
          on:dragover={handleDragOver}
          on:dragleave={handleDragLeave}
          on:drop={handleDrop}
          on:keydown={(e) =>
            e.key === "Enter" &&
            document.getElementById("apkFileInput")?.click()}
          role="button"
          tabindex="0"
          aria-label="Drop APK file here or click to browse"
        >
          {#if selectedFile}
            <div class="selected-file">
              <span class="file-name">{selectedFile.name}</span>
              <span class="file-size"
                >({(selectedFile.size / (1024 * 1024)).toFixed(2)} MB)</span
              >
              <button
                type="button"
                class="clear-file"
                on:click={clearSelectedFile}>×</button
              >
            </div>
          {:else}
            <div class="drop-message">
              <span>Drag & drop APK file here or</span>
              <button
                type="button"
                class="browse-button"
                on:click={(e) => {
                  e.stopPropagation();

                  // Prevent multiple rapid clicks
                  if (fileInputLocked) {
                    console.log("File input locked, ignoring click");
                    return;
                  }

                  // Lock the file input to prevent multiple clicks
                  fileInputLocked = true;

                  // Clear any existing debounce
                  if (fileSelectDebounce) {
                    clearTimeout(fileSelectDebounce);
                  }

                  // Trigger the file input
                  const fileInput = document.getElementById("apkFileInput");
                  if (fileInput instanceof HTMLInputElement) {
                    fileInput.click();

                    // Unlock after a short delay
                    fileSelectDebounce = setTimeout(() => {
                      fileInputLocked = false;
                      fileSelectDebounce = null;
                    }, 500); // 500ms lock
                  } else {
                    fileInputLocked = false;
                  }
                }}
              >
                Browse
              </button>
              <span class="file-requirements"
                >File must be a valid APK file (at least 1MB in size)</span
              >
            </div>
          {/if}
          <input
            type="file"
            id="apkFileInput"
            name="apkFile"
            accept=".apk,application/vnd.android.package-archive"
            on:change={handleFileSelect}
            style="display: none;"
          />
        </div>
      </div>

      <!-- Configuration Text Area with Asset Filename Field -->
      <div class="form-group">
        <label for="configTextarea" class="form-label">Configuration</label>
        <textarea
          id="configTextarea"
          name="config"
          class="config-textarea"
          bind:value={configText}
          rows="10"
        ></textarea>

        <!-- Asset Filename Field (now inside the Configuration form group) -->
        <div class="asset-filename-container">
          <label for="assetFilename" class="form-label">Asset Filename</label>
          <input
            type="text"
            id="assetFilename"
            name="assetFilename"
            class="config-textarea asset-filename-input"
            bind:value={assetFilename}
            placeholder="vpn.conf"
          />
          <div class="asset-filename-help">
            Name of the configuration file in the APK assets folder
          </div>
        </div>
      </div>

      <!-- Submit Button -->
      <div class="form-actions">
        {#if downloadUrl}
          <!-- Show download button when APK is ready -->
          <div class="download-actions">
            <button
              type="button"
              class="button reset-button"
              on:click={() => {
                downloadUrl = "";
                downloadFilename = "";
                selectedFile = null;

                // Reset configuration to default value
                configText = defaultConfig;

                // Reset asset filename to default value
                assetFilename = "vpn.conf";

                // Also clear the file input
                const fileInput = document.getElementById("apkFileInput");
                if (fileInput && fileInput instanceof HTMLInputElement) {
                  fileInput.value = "";
                }
              }}
            >
              Repack Another
            </button>
            <a
              href={downloadUrl}
              download={downloadFilename}
              class="button download-button"
            >
              Download Repacked APK
            </a>
          </div>
        {:else}
          <!-- Show repack button when no APK is ready -->
          <button
            type="submit"
            class="button"
            disabled={!selectedFile || !configText || processing}
          >
            {#if processing}
              Processing...
            {:else}
              Repack APK
            {/if}
          </button>
        {/if}

        <!-- Fallback button (hidden by default, shown only when needed) -->
        <button
          type="button"
          class="button button-fallback"
          on:click={submitForm}
          disabled={!selectedFile || !configText || processing}
          style="display: none;"
        >
          Direct Submit
        </button>
      </div>
    </form>

    <!-- Download section removed - integrated into form actions -->
  </div>
</div>