import { IRepository } from './IRepository.js';
import { QueryOptions, QueryResult } from './IDatabase.js';

/**
 * Device repository interface
 * Defines all device-related database operations
 */
export class IDeviceRepository extends IRepository {
  /**
   * Find devices by team ID
   * @param {string} teamId - Team's public ID
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findByTeamId(teamId, options = new QueryOptions()) {
    throw new Error('findByTeamId() method must be implemented');
  }

  /**
   * Find device by IP address
   * @param {string} ip - Device IP address
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findByIP(ip, options = new QueryOptions()) {
    throw new Error('findByIP() method must be implemented');
  }

  /**
   * Find devices with team information (joined data)
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findWithTeamInfo(options = new QueryOptions()) {
    throw new Error('findWithTeamInfo() method must be implemented');
  }

  /**
   * Find recently active devices
   * @param {Date} since - Date threshold for recent activity
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findRecentlyActive(since, options = new QueryOptions()) {
    throw new Error('findRecentlyActive() method must be implemented');
  }

  /**
   * Find recently created devices
   * @param {Date} since - Date threshold for recent creation
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findRecentlyCreated(since, options = new QueryOptions()) {
    throw new Error('findRecentlyCreated() method must be implemented');
  }

  /**
   * Find devices with FCM tokens (can receive notifications)
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findWithFCMTokens(options = new QueryOptions()) {
    throw new Error('findWithFCMTokens() method must be implemented');
  }

  /**
   * Update device authentication timestamp
   * @param {string} deviceId - Device internal ID
   * @param {Date} authTime - Authentication timestamp
   * @returns {Promise<QueryResult>}
   */
  async updateAuthTimestamp(deviceId, authTime = new Date()) {
    throw new Error('updateAuthTimestamp() method must be implemented');
  }

  /**
   * Update device FCM token
   * @param {string} deviceId - Device internal ID
   * @param {string} fcmToken - FCM token
   * @returns {Promise<QueryResult>}
   */
  async updateFCMToken(deviceId, fcmToken) {
    throw new Error('updateFCMToken() method must be implemented');
  }

  /**
   * Update device FCM token by IP address
   * @param {string} ip - Device IP address
   * @param {string} fcmToken - FCM token
   * @returns {Promise<QueryResult>}
   */
  async updateFCMTokenByIP(ip, fcmToken) {
    throw new Error('updateFCMTokenByIP() method must be implemented');
  }

  /**
   * Update device configuration
   * @param {string} deviceId - Device internal ID
   * @param {Object} config - Configuration object
   * @param {string} config.vpn_conf - VPN configuration
   * @param {string} config.msg_conf - Message configuration
   * @param {string} config.phone_conf - Phone configuration
   * @returns {Promise<QueryResult>}
   */
  async updateConfiguration(deviceId, config) {
    throw new Error('updateConfiguration() method must be implemented');
  }

  /**
   * Get device statistics
   * @param {Object} options - Statistics options
   * @param {Date} options.since - Date threshold for recent data
   * @param {number} options.activeHoursThreshold - Hours threshold for active devices
   * @returns {Promise<QueryResult>}
   */
  async getStatistics(options = {}) {
    throw new Error('getStatistics() method must be implemented');
  }

  /**
   * Find devices by role
   * @param {string} role - Device role
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findByRole(role, options = new QueryOptions()) {
    throw new Error('findByRole() method must be implemented');
  }

  /**
   * Find devices by nickname pattern
   * @param {string} pattern - Nickname search pattern
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findByNickname(pattern, options = new QueryOptions()) {
    throw new Error('findByNickname() method must be implemented');
  }

  /**
   * Count active devices for a team
   * @param {string} teamId - Team's public ID
   * @param {Date} since - Date threshold for activity
   * @returns {Promise<QueryResult>}
   */
  async countActiveByTeam(teamId, since) {
    throw new Error('countActiveByTeam() method must be implemented');
  }

  /**
   * Get last connected device
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async getLastConnected(options = new QueryOptions()) {
    throw new Error('getLastConnected() method must be implemented');
  }

  /**
   * Bulk update device authentication timestamps
   * @param {Array<{deviceId: string, authTime: Date}>} updates - Array of auth updates
   * @returns {Promise<QueryResult>}
   */
  async bulkUpdateAuthTimestamps(updates) {
    throw new Error('bulkUpdateAuthTimestamps() method must be implemented');
  }

  /**
   * Delete devices by team ID
   * @param {string} teamId - Team's public ID
   * @returns {Promise<QueryResult>}
   */
  async deleteByTeamId(teamId) {
    throw new Error('deleteByTeamId() method must be implemented');
  }

  /**
   * Find orphaned devices (devices without valid team)
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findOrphanedDevices(options = new QueryOptions()) {
    throw new Error('findOrphanedDevices() method must be implemented');
  }

  /**
   * Get device activity report
   * @param {Date} since - Start date for report
   * @param {Date} until - End date for report
   * @returns {Promise<QueryResult>}
   */
  async getActivityReport(since, until = new Date()) {
    throw new Error('getActivityReport() method must be implemented');
  }

  /**
   * Get table name for this repository
   * @returns {string}
   */
  getTableName() {
    return 'devices';
  }
}
